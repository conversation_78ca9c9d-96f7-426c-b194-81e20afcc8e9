<?php return array (
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'diglactic/laravel-breadcrumbs' => 
  array (
    'aliases' => 
    array (
      'Breadcrumbs' => 'Diglactic\\Breadcrumbs\\Breadcrumbs',
    ),
    'providers' => 
    array (
      0 => 'Diglactic\\Breadcrumbs\\ServiceProvider',
    ),
  ),
  'konekt/concord' => 
  array (
    'aliases' => 
    array (
      'Helper' => 'Konekt\\Concord\\Facades\\Helper',
      'Concord' => 'Konekt\\Concord\\Facades\\Concord',
    ),
    'providers' => 
    array (
      0 => 'Konekt\\Concord\\ConcordServiceProvider',
    ),
  ),
  'konekt/enum-eloquent' => 
  array (
    'providers' => 
    array (
      0 => 'Konekt\\Enum\\Eloquent\\EnumServiceProvider',
    ),
  ),
  'krayin/krayin-package-generator' => 
  array (
    'aliases' => 
    array (
    ),
    'providers' => 
    array (
      0 => 'Webkul\\PackageGenerator\\Providers\\PackageGeneratorServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'prettus/l5-repository' => 
  array (
    'providers' => 
    array (
      0 => 'Prettus\\Repository\\Providers\\RepositoryServiceProvider',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
  ),
  'webklex/laravel-imap' => 
  array (
    'aliases' => 
    array (
      'Client' => 'Webklex\\IMAP\\Facades\\Client',
    ),
    'providers' => 
    array (
      0 => 'Webklex\\IMAP\\Providers\\LaravelServiceProvider',
    ),
  ),
);