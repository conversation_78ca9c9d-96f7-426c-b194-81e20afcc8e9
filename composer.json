{"name": "krayin/laravel-crm", "type": "project", "description": "<PERSON>rayin <PERSON>", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^2.0.0", "diglactic/laravel-breadcrumbs": "^8.0", "doctrine/dbal": "^3.0", "enshrined/svg-sanitize": "^0.21.0", "guzzlehttp/guzzle": "^7.0.1", "khaled.alshamaa/ar-php": "^6.3", "konekt/concord": "^1.10", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.5", "laravel/ui": "^4.5", "maatwebsite/excel": "^3.1", "mpdf/mpdf": "^8.2", "prettus/l5-repository": "^2.7.9", "smalot/pdfparser": "^2.11", "webklex/laravel-imap": "^5.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "krayin/krayin-package-generator": "dev-master", "laravel/pint": "^1.16", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.6", "pestphp/pest-plugin-laravel": "^2.1", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Webkul\\Activity\\": "packages/Webkul/Activity/src", "Webkul\\Admin\\": "packages/Webkul/Admin/src", "Webkul\\Attribute\\": "packages/Webkul/Attribute/src", "Webkul\\Contact\\": "packages/Webkul/Contact/src", "Webkul\\Core\\": "packages/Webkul/Core/src", "Webkul\\DataGrid\\": "packages/Webkul/DataGrid/src", "Webkul\\DataTransfer\\": "packages/Webkul/DataTransfer/src", "Webkul\\Email\\": "packages/Webkul/Email/src", "Webkul\\EmailTemplate\\": "packages/Webkul/EmailTemplate/src", "Webkul\\Marketing\\": "packages/Webkul/Marketing/src", "Webkul\\Installer\\": "packages/Webkul/Installer/src", "Webkul\\Lead\\": "packages/Webkul/Lead/src", "Webkul\\Product\\": "packages/Webkul/Product/src", "Webkul\\Quote\\": "packages/Webkul/Quote/src", "Webkul\\Tag\\": "packages/Webkul/Tag/src", "Webkul\\User\\": "packages/Webkul/User/src", "Webkul\\Warehouse\\": "packages/Webkul/Warehouse/src", "Webkul\\WebForm\\": "packages/Webkul/WebForm/src", "Webkul\\Automation\\": "packages/Webkul/Automation/src", "Webkul\\Sales\\": "packages/Webkul/Sales/src"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "repositories": [{"type": "path", "url": "packages/*/*", "options": {"symlink": true}}], "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}