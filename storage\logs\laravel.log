[2025-07-09 23:54:57] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Routes\\Admin\\auth-routes.php:9)
[stacktrace]
#0 {main}
"} 
[2025-07-10 00:12:18] local.ERROR: Class "Webkul\Sales\Providers\SalesServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Webkul\\Sales\\Providers\\SalesServiceProvider\" not found at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#10 {main}
"} 
[2025-07-10 00:13:53] local.ERROR: Class "Webkul\Sales\Providers\SalesServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Webkul\\Sales\\Providers\\SalesServiceProvider\" not found at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#10 {main}
"} 
[2025-07-10 00:15:13] local.ERROR: Class "Webkul\Sales\Providers\SalesServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Webkul\\Sales\\Providers\\SalesServiceProvider\" not found at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#10 {main}
"} 
[2025-07-10 00:26:38] local.ERROR: Class "Webkul\Sales\Providers\SalesServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Webkul\\Sales\\Providers\\SalesServiceProvider\" not found at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#8 {main}
"} 
[2025-07-10 00:27:54] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sales_targets' already exists (Connection: mysql, SQL: create table `sales_targets` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `description` text null, `target_amount` decimal(15, 2) not null, `achieved_amount` decimal(15, 2) not null default '0', `assignee_type` enum('individual', 'team', 'region') not null, `assignee_id` bigint unsigned not null, `assignee_name` varchar(255) not null, `start_date` date not null, `end_date` date not null, `period_type` enum('daily', 'weekly', 'monthly', 'quarterly', 'half_yearly', 'annual', 'custom') not null, `status` enum('active', 'completed', 'paused', 'cancelled') not null default 'active', `progress_percentage` decimal(5, 2) not null default '0', `last_calculated_at` timestamp null, `notes` text null, `attachments` json null, `metadata` json null, `created_by` bigint unsigned not null, `updated_by` bigint unsigned null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sales_targets' already exists (Connection: mysql, SQL: create table `sales_targets` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `description` text null, `target_amount` decimal(15, 2) not null, `achieved_amount` decimal(15, 2) not null default '0', `assignee_type` enum('individual', 'team', 'region') not null, `assignee_id` bigint unsigned not null, `assignee_name` varchar(255) not null, `start_date` date not null, `end_date` date not null, `period_type` enum('daily', 'weekly', 'monthly', 'quarterly', 'half_yearly', 'annual', 'custom') not null, `status` enum('active', 'completed', 'paused', 'cancelled') not null default 'active', `progress_percentage` decimal(5, 2) not null default '0', `last_calculated_at` timestamp null, `notes` text null, `attachments` json null, `metadata` json null, `created_by` bigint unsigned not null, `updated_by` bigint unsigned null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000001_create_sales_targets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sales_targets' already exists at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000001_create_sales_targets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
[2025-07-10 00:28:39] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#19 {main}
"} 
[2025-07-10 00:28:56] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#19 {main}
"} 
[2025-07-10 00:29:04] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `laravel-crm`.`sales_target_adjustments` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `sales_target_adjustments` add constraint `sales_target_adjustments_sales_target_id_foreign` foreign key (`sales_target_id`) references `sales_targets` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `laravel-crm`.`sales_target_adjustments` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `sales_target_adjustments` add constraint `sales_target_adjustments_sales_target_id_foreign` foreign key (`sales_target_id`) references `sales_targets` (`id`) on delete cascade) at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000002_create_sales_target_adjustments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `laravel-crm`.`sales_target_adjustments` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000002_create_sales_target_adjustments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
[2025-07-10 00:29:40] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#19 {main}
"} 
[2025-07-10 00:30:05] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `laravel-crm`.`sales_targets` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `sales_targets` add constraint `sales_targets_created_by_foreign` foreign key (`created_by`) references `users` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `laravel-crm`.`sales_targets` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `sales_targets` add constraint `sales_targets_created_by_foreign` foreign key (`created_by`) references `users` (`id`) on delete cascade) at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000001_create_sales_targets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `laravel-crm`.`sales_targets` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000001_create_sales_targets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
[2025-07-10 00:33:16] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sales_targets' already exists (Connection: mysql, SQL: create table `sales_targets` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `description` text null, `target_amount` decimal(15, 2) not null, `achieved_amount` decimal(15, 2) not null default '0', `assignee_type` enum('individual', 'team', 'region') not null, `assignee_id` int unsigned not null, `assignee_name` varchar(255) not null, `start_date` date not null, `end_date` date not null, `period_type` enum('daily', 'weekly', 'monthly', 'quarterly', 'half_yearly', 'annual', 'custom') not null, `status` enum('active', 'completed', 'paused', 'cancelled') not null default 'active', `progress_percentage` decimal(5, 2) not null default '0', `last_calculated_at` timestamp null, `notes` text null, `attachments` json null, `metadata` json null, `created_by` int unsigned not null, `updated_by` int unsigned null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sales_targets' already exists (Connection: mysql, SQL: create table `sales_targets` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `description` text null, `target_amount` decimal(15, 2) not null, `achieved_amount` decimal(15, 2) not null default '0', `assignee_type` enum('individual', 'team', 'region') not null, `assignee_id` int unsigned not null, `assignee_name` varchar(255) not null, `start_date` date not null, `end_date` date not null, `period_type` enum('daily', 'weekly', 'monthly', 'quarterly', 'half_yearly', 'annual', 'custom') not null, `status` enum('active', 'completed', 'paused', 'cancelled') not null default 'active', `progress_percentage` decimal(5, 2) not null default '0', `last_calculated_at` timestamp null, `notes` text null, `attachments` json null, `metadata` json null, `created_by` int unsigned not null, `updated_by` int unsigned null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000001_create_sales_targets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sales_targets' already exists at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Database\\Migrations\\2024_01_01_000001_create_sales_targets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
