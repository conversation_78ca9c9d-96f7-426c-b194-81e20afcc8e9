<?php

namespace Webkul\Sales\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Webkul\Sales\Repositories\SalesTargetRepository;
use Webkul\Sales\Repositories\SalesPerformanceRepository;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected SalesTargetRepository $salesTargetRepository,
        protected SalesPerformanceRepository $salesPerformanceRepository
    ) {
    }

    /**
     * Display the sales dashboard.
     */
    public function index(): View
    {
        return view('sales::dashboard.index');
    }

    /**
     * Get dashboard statistics.
     */
    public function stats(): JsonResponse
    {
        $type = request('type', 'overview');

        switch ($type) {
            case 'overview':
                return $this->getOverviewStats();
            case 'targets':
                return $this->getTargetsStats();
            case 'performance':
                return $this->getPerformanceStats();
            case 'leaderboard':
                return $this->getLeaderboardStats();
            default:
                return new JsonResponse(['error' => 'Invalid stats type'], 400);
        }
    }

    /**
     * Get overview statistics.
     */
    protected function getOverviewStats(): JsonResponse
    {
        $targetsSummary = $this->salesTargetRepository->getTargetsSummary();
        $performanceSummary = $this->salesPerformanceRepository->getPerformanceSummary();

        return new JsonResponse([
            'targets' => $targetsSummary,
            'performance' => $performanceSummary,
        ]);
    }

    /**
     * Get targets statistics.
     */
    protected function getTargetsStats(): JsonResponse
    {
        $period = request('period', 'monthly');
        $dateFrom = request('date_from', now()->subMonths(6)->format('Y-m-d'));
        $dateTo = request('date_to', now()->format('Y-m-d'));

        $targetsOverTime = $this->salesTargetRepository->getTargetsOverTime($period, $dateFrom, $dateTo);
        $targetsByType = $this->salesTargetRepository->getTargetsByAssigneeType();
        $targetsByStatus = $this->salesTargetRepository->getTargetsByStatus();

        return new JsonResponse([
            'over_time' => $targetsOverTime,
            'by_type' => $targetsByType,
            'by_status' => $targetsByStatus,
        ]);
    }

    /**
     * Get performance statistics.
     */
    protected function getPerformanceStats(): JsonResponse
    {
        $period = request('period', 'monthly');
        $dateFrom = request('date_from', now()->subMonths(6)->format('Y-m-d'));
        $dateTo = request('date_to', now()->format('Y-m-d'));

        $performanceOverTime = $this->salesPerformanceRepository->getPerformanceOverTime($period, $dateFrom, $dateTo);
        $achievementRates = $this->salesPerformanceRepository->getAchievementRates($period, $dateFrom, $dateTo);

        return new JsonResponse([
            'over_time' => $performanceOverTime,
            'achievement_rates' => $achievementRates,
        ]);
    }

    /**
     * Get leaderboard statistics.
     */
    protected function getLeaderboardStats(): JsonResponse
    {
        $period = request('period', 'monthly');
        $type = request('leaderboard_type', 'individual');
        $limit = request('limit', 10);

        $leaderboard = $this->salesPerformanceRepository->getLeaderboard($type, $period, $limit);

        return new JsonResponse([
            'leaderboard' => $leaderboard,
        ]);
    }
}
